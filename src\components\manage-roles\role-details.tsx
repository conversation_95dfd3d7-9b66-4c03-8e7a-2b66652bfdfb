import React, { useState, useEffect } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { GetPermissionList } from '@/api/staff';
import { LoadingState } from '@/components/common/dataState';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { Modal } from '@/components/common/modal';

interface RoleDetailsProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  role: any;
}

const RoleDetails: React.FC<RoleDetailsProps> = ({ open, setOpen, role }) => {
  const [isSaveLoading, setIsSaveLoading] = useState(false);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const { permissionList: permissions, permissionLoading } =
    GetPermissionList();

  useEffect(() => {
    if (role?.permissions) {
      setSelectedPermissions(role.permissions.map((p: any) => p.id));
    }
  }, [role]);

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    if (checked) {
      setSelectedPermissions([...selectedPermissions, permissionId]);
    } else {
      setSelectedPermissions(
        selectedPermissions.filter((id) => id !== permissionId)
      );
    }
  };

  const handleSave = async () => {
    setIsSaveLoading(true);
    const payload = {
      roleId: role?.id,
      permissionIds: selectedPermissions,
    };
    const res = await myApi.patch('/staff/update-role', payload);
    if (res.status === 200) {
      toast.success('Role updated successfully');
      setOpen(false);
    } else {
      toast.error('Failed to update role');
    }
    setIsSaveLoading(false);
  };

  if (!role) return null;

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title={`Manage Role: ${role.name}`}
      description="Edit role permissions"
      isLoading={isSaveLoading}
      onSubmit={handleSave}
    >
      <div className="space-y-4">
        <div>
          <h3 className="text-sm font-medium mb-2">Role Description</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {role.description || 'No description available'}
          </p>
        </div>

        <div>
          <h3 className="text-sm font-medium mb-3">Permissions</h3>
          {permissionLoading ? (
            <LoadingState />
          ) : (
            <div className="space-y-2 max-h-60 overflow-y-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {permissions.map((permission: any) => (
                  <div
                    key={permission.id}
                    className="flex items-center space-x-2"
                  >
                    <Checkbox
                      id={permission.id}
                      checked={selectedPermissions.includes(permission.id)}
                      onCheckedChange={(checked) =>
                        handlePermissionChange(
                          permission.id,
                          checked as boolean
                        )
                      }
                    />
                    <label
                      htmlFor={permission.id}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {permission.action}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default RoleDetails;
