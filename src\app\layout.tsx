import { Poppins } from 'next/font/google';
import './globals.css';
import { ThemeProvider } from '@/components/theme-provider';
import ProgressBar from '@/Providers/progressBar';
import { Toaster } from '@/components/ui/sonner';
import Script from 'next/script';

const poppins = Poppins({
  subsets: ['latin'],
  variable: '--font-poppins',
  display: 'swap',
  weight: ['300', '400', '700'],
});

export const metadata = {
  title: 'Cedarcrest Hospitals Innovation Suite',
  description: 'Everything Cedarcrest Innovations happens here...',
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'Cedarcrest Dashboard',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="manifest" href="/manifest.json" />
        <link rel="apple-touch-icon" href="/icon.png" />
        <meta name="theme-color" content="#000000" />
      </head>
      <body className={`${poppins.variable}`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <ProgressBar>
            {children}
            <Toaster richColors position="top-center" />
          </ProgressBar>
        </ThemeProvider>
        <Script
          id="register-sw"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', async function() {
                  try {
                    const registration = await navigator.serviceWorker.register('/sw.js', { scope: '/' });
                    console.log('Service Worker registration successful with scope:', registration.scope);
                  } catch (error) {
                    console.error('Service Worker registration failed:', error);
                  }
                });
              }
            `,
          }}
        />
      </body>
    </html>
  );
}
