// 'use client';

// import React, { useState, useCallback } from 'react';
// import { Lightbulb, RefreshCw } from 'lucide-react';
// import { Button } from '@/components/ui/button';
// import { toast } from 'sonner';
// import { PERMISSIONS, hasPermission } from '@/lib/types/permissions';
// import { useDebounce } from '@/hooks/useDebounce';
// import {
//   GetIdeas,
//   GetIdeaStats,
//   createIdea,
//   toggleIdeaLike,
//   updateIdeaStatus,
//   addIdeaComment,
//   deleteIdea,
//   type Idea,
//   type IdeaStatus,
//   type SearchParams,
// } from '@/api/innovation-hub/data';
// import { IdeaFormValues } from './components/types';
// import { IdeaCard } from './components/idea-card';
// import { CommentModal } from './components/comment-modal';
// import { SubmitIdeaForm } from './components/submit-idea-form';
// import { SearchFilter } from './components/search-filter';
// import { Pagination } from './components/pagination';
// import { LoadingState } from './components/loading-state';
// import { ErrorState } from './components/error-state';
// import { StatsCards } from './components/stats-cards';
// import { EmptyState } from './components/empty-state';

// export default function InnovationHubContent() {
//   const [searchQuery, setSearchQuery] = useState('');
//   const [statusFilter, setStatusFilter] = useState<IdeaStatus | 'all'>('all');
//   const [sortBy, setSortBy] = useState<'createdAt' | 'likes' | 'title'>('createdAt');
//   const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
//   const [currentPage, setCurrentPage] = useState(1);
//   const [isCommentModalOpen, setCommentModalOpen] = useState(false);
//   const [selectedIdeaForComments, setSelectedIdeaForComments] = useState<Idea | null>(null);
  
//   const debouncedSearchQuery = useDebounce(searchQuery, 300);
//   const canEdit = hasPermission(PERMISSIONS.HUB_EDIT);

//   const searchParams: SearchParams = {
//     query: debouncedSearchQuery || undefined,
//     status: statusFilter === 'all' ? undefined : statusFilter,
//     page: currentPage,
//     limit: 10,
//     sortBy,
//     sortOrder,
//   };

//   const { ideas, total, totalPages, isLoading, error, mutate } = GetIdeas(searchParams);
//   const { stats, isLoading: statsLoading } = GetIdeaStats();

//   const handleIdeaSubmit = async (values: IdeaFormValues) => {
//     try {
//       await createIdea(values);
//       mutate();
//     } catch (error) {
//       console.error('Error submitting idea:', error);
//     }
//   };

//   const handleLike = async (id: string) => {
//     try {
//       await toggleIdeaLike(id);
//       mutate();
//     } catch (error) {
//       console.error('Error liking idea:', error);
//     }
//   };

//   const handleDelete = async (id: string) => {
//     try {
//       await deleteIdea(id);
//       mutate();
//     } catch (error) {
//       console.error('Error deleting idea:', error);
//     }
//   };

//   const handleStatusUpdate = async (id: string, newStatus: IdeaStatus) => {
//     try {
//       await updateIdeaStatus(id, newStatus);
//       mutate();
//     } catch (error) {
//       console.error('Error updating status:', error);
//     }
//   };

//   const handleOpenCommentModal = (idea: Idea) => {
//     setSelectedIdeaForComments(idea);
//     setCommentModalOpen(true);
//   };

//   const handleCommentSubmit = async (commentText: string) => {
//     if (!selectedIdeaForComments) return;
    
//     try {
//       await addIdeaComment(selectedIdeaForComments.id, commentText);
//       setCommentModalOpen(false);
//       mutate();
//     } catch (error) {
//       console.error('Error adding comment:', error);
//     }
//   };

//   const handleSearchChange = useCallback((query: string) => {
//     setSearchQuery(query);
//     setCurrentPage(1);
//   }, []);

//   const handleStatusChange = useCallback((status: IdeaStatus | 'all') => {
//     setStatusFilter(status);
//     setCurrentPage(1);
//   }, []);

//   const handleSortChange = useCallback((sort: 'createdAt' | 'likes' | 'title') => {
//     setSortBy(sort);
//     setCurrentPage(1);
//   }, []);

//   const handleSortOrderChange = useCallback((order: 'asc' | 'desc') => {
//     setSortOrder(order);
//     setCurrentPage(1);
//   }, []);

//   const handlePageChange = useCallback((page: number) => {
//     setCurrentPage(page);
//   }, []);

//   return (
//     <div className="space-y-6">
//       {/* Header */}
//       <div className="flex items-center justify-between">
//         <div className="flex items-center space-x-3">
//           <div className="p-2 bg-primary/10 rounded-lg">
//             <Lightbulb className="h-6 w-6 text-primary" />
//           </div>
//           <div>
//             <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
//               Innovation Hub
//             </h1>
//             <p className="text-gray-600 dark:text-gray-400">
//               Explore, share, and collaborate on cutting-edge ideas.
//             </p>
//           </div>
//         </div>
//         <Button
//           variant="outline"
//           size="sm"
//           onClick={() => mutate()}
//           disabled={isLoading}
//         >
//           <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
//           Refresh
//         </Button>
//       </div>

//       <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
//         {/* Submit Idea Form */}
//         <div className="lg:col-span-1">
//           <SubmitIdeaForm onIdeaSubmit={handleIdeaSubmit} />
//         </div>

//         {/* Ideas Feed */}
//         <div className="lg:col-span-2 space-y-6">
//           <div className="flex items-center justify-between">
//             <h2 className="text-xl font-semibold">Ideas ({total})</h2>
//           </div>
          
//           {!statsLoading && (
//             <StatsCards
//               totalIdeas={stats.totalIdeas}
//               approvedIdeas={stats.approvedIdeas}
//               underReviewIdeas={stats.underReviewIdeas}
//               implementedIdeas={stats.implementedIdeas}
//             />
//           )}
          
//           <SearchFilter
//             searchQuery={searchQuery}
//             onSearchChange={handleSearchChange}
//             statusFilter={statusFilter}
//             onStatusChange={handleStatusChange}
//             sortBy={sortBy}
//             onSortChange={handleSortChange}
//             sortOrder={sortOrder}
//             onSortOrderChange={handleSortOrderChange}
//           />
          
//           {error ? (
//             <ErrorState error={error} onRetry={() => mutate()} />
//           ) : isLoading ? (
//             <LoadingState />
//           ) : ideas.length === 0 ? (
//             <EmptyState 
//               title={searchQuery || statusFilter !== 'all' ? 'No ideas match your search' : 'No ideas yet'}
//               description={searchQuery || statusFilter !== 'all' ? 'Try adjusting your search criteria or filters.' : 'Be the first to share your innovative idea and inspire others!'}
//               actionText="Clear Filters"
//               onAction={searchQuery || statusFilter !== 'all' ? () => {
//                 setSearchQuery('');
//                 setStatusFilter('all');
//                 setCurrentPage(1);
//               } : undefined}
//             />
//           ) : (
//             <>
//               {ideas.map((idea: any) => (
//                 <IdeaCard
//                   key={idea.id}
//                   idea={idea}
//                   onLike={handleLike}
//                   canEdit={canEdit}
//                   onDelete={handleDelete}
//                   onStatusUpdate={handleStatusUpdate}
//                   onCommentClick={handleOpenCommentModal}
//                 />
//               ))}
              
//               <Pagination
//                 currentPage={currentPage}
//                 totalPages={totalPages}
//                 onPageChange={handlePageChange}
//                 total={total}
//                 limit={10}
//               />
//             </>
//           )}
//         </div>
//       </div>
      
//       <CommentModal
//         idea={selectedIdeaForComments}
//         open={isCommentModalOpen}
//         onOpenChange={setCommentModalOpen}
//         onCommentSubmit={handleCommentSubmit}
//       />
//     </div>
//   );
// }
'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Lightbulb, Construction, Sparkles } from 'lucide-react';

export default function InnovationHubContent() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="p-2 bg-primary/10 rounded-lg">
          <Lightbulb className="h-6 w-6 text-primary" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Innovation Hub
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Explore cutting-edge features and innovations
          </p>
        </div>
      </div>

      {/* Coming Soon Card */}
      <Card className="max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="relative">
              <div className="p-4 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full">
                <Construction className="h-12 w-12 text-primary" />
              </div>
              <div className="absolute -top-1 -right-1">
                <Sparkles className="h-6 w-6 text-yellow-500 animate-pulse" />
              </div>
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
            Feature Coming Soon!
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-lg text-gray-600 dark:text-gray-400">
            Currently in Development
          </p>
          <div className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-lg p-6">
            <p className="text-gray-700 dark:text-gray-300">
              We're building a dynamic hub where staff can explore the latest
              innovations in progress and contribute their own ideas. This space
              is designed to foster creativity, showcase ongoing technological
              advancements, and empower everyone to shape the future of our
              workflows.
            </p>
          </div>
          <div className="flex justify-center">
            <div className="flex space-x-2">
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
              <div
                className="w-2 h-2 bg-primary rounded-full animate-bounce"
                style={{ animationDelay: '0.1s' }}
              ></div>
              <div
                className="w-2 h-2 bg-primary rounded-full animate-bounce"
                style={{ animationDelay: '0.2s' }}
              ></div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
