'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import dayjs from 'dayjs';
import { GetReferralDetails } from '@/api/referral/data';
import { getStatusColor, getUrgencyColor } from '@/lib/utils';

interface ReferralDetailsProps {
  referralId: string;
  onBack: () => void;
}

export default function ReferralDetails({
  referralId,
  onBack,
}: ReferralDetailsProps) {
  const { referralDetails, detailsLoading, mutate } =
    GetReferralDetails(referralId);
  const referral = referralDetails?.data;
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [showSender, setShowSender] = useState(true);

  if (detailsLoading) {
    return (
      <div className="mt-6">
        <Card className="col-span-6 md:col-span-4 p-4">
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
              <p>Loading referral details...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!referral) {
    return (
      <div className="mt-6">
        <Card className="col-span-6 md:col-span-4 p-4">
          <CardHeader className="flex flex-row items-center justify-between">
            <div className="flex items-center gap-4">
              <Button variant="outline" size="icon" onClick={onBack}>
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <CardTitle className="text-2xl">Referral Details</CardTitle>
                <CardDescription>View referral information</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="flex items-center justify-center py-8">
            <p>Referral not found</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="mt-6">
      <Card className="p-4">
        <CardHeader className="flex flex-wrap items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="icon" onClick={onBack}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <CardTitle className="text-xl">Referral Details</CardTitle>
              <CardDescription>View referral information</CardDescription>
            </div>
          </div>
          <div className="flex flex-wrap gap-2 items-center">
            {referral.urgency && (
              <span
                className={`inline-flex items-center rounded-full px-3 py-1 text-xs ${getUrgencyColor(referral.urgency)}`}
              >
                {referral.urgency.charAt(0).toUpperCase() +
                  referral.urgency.slice(1).toLowerCase()}
              </span>
            )}
            <span
              className={`inline-flex items-center rounded-full px-3 py-1 text-xs ${getStatusColor(referral.status)}`}
            >
              {referral.status
                .replace(/_/g, ' ')
                .toLowerCase()
                .replace(/\b\w/g, (l: string) => l.toUpperCase())}
            </span>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Patient Information</h3>
              <div className="space-y-2">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    First Name / Last Name
                  </label>
                  <p className="text-sm">
                    {referral.patient.firstName} {referral.patient.lastName}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Email
                  </label>
                  <p className="text-sm">{referral.patient.emailAddress}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Phone Number
                  </label>
                  <p className="text-sm">{referral.patient.phoneNumber}</p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Referral Information</h3>
              <div className="space-y-2">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Referral ID
                  </label>
                  <p className="text-sm">{referral.referralID}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Referral Type
                  </label>
                  <p className="text-sm">{referral.referralType}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Date Created
                  </label>
                  <p className="text-sm">
                    {dayjs(referral.createdAt).format('MMMM D, YYYY')}
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-2 bg-secondary/10 rounded-md p-2">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-lg font-semibold">
                  {showSender ? 'Sender' : 'Receiver'} Information
                </h3>
                <div className="flex gap-1">
                  <Button
                    variant={showSender ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setShowSender(true)}
                  >
                    Sender
                  </Button>
                  <Button
                    variant={!showSender ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setShowSender(false)}
                  >
                    Receiver
                  </Button>
                </div>
              </div>
              {/* <div className="space-y-2">
                {showSender ? (
                  // Sender Information
                  <>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Name
                      </label>
                      <p className="text-sm">
                        {referral.referringEntity.entityType === "ORGANIZATION"
                          ? referral.referringEntity.primaryContactName
                          : referral.referringEntity.name}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Phone Number
                      </label>
                      <p className="text-sm">
                        {referral.referringEntity.entityType === "ORGANIZATION"
                          ? referral.referringEntity.primaryContactPhone
                          : referral.referringEntity.phoneNumber}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Email Address
                      </label>
                      <p className="text-sm">
                        {referral.referringEntity.entityType === "ORGANIZATION"
                          ? referral.referringEntity.primaryContactEmail
                          : referral.referringEntity.email}
                      </p>
                    </div>
                  </>
                ) : (
                  // Receiver Information
                  <>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Name
                      </label>
                      <p className="text-sm">
                        {referral.receivingEntity.entityType === "ORGANIZATION"
                          ? referral.receivingEntity.primaryContactName
                          : referral.receivingEntity.name}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Phone Number
                      </label>
                      <p className="text-sm">
                        {referral.receivingEntity.entityType === "ORGANIZATION"
                          ? referral.receivingEntity.primaryContactPhone
                          : referral.receivingEntity.phoneNumber}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Email Address
                      </label>
                      <p className="text-sm">
                        {referral.receivingEntity.entityType === "ORGANIZATION"
                          ? referral.receivingEntity.primaryContactEmail
                          : referral.receivingEntity.email}
                      </p>
                    </div>
                  </>
                )}
              </div> */}
            </div>
          </div>

          {referral.reasonForReferral && (
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Reason</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm whitespace-pre-wrap">
                  {referral.reasonForReferral}
                </p>
              </div>
            </div>
          )}

          {(referral.comments?.length > 0 || referral.commentNeeded) && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Comments</h3>

              {referral.comments?.length > 0 && (
                <div className="space-y-3">
                  {referral?.comments?.map((comment: any, index: number) => (
                    <div
                      key={index}
                      className="bg-gray-50 p-4 rounded-lg border-l-4 border-blue-800"
                    >
                      <div className="flex justify-between items-start mb-2">
                        <span className="text-sm font-medium text-blue-800">
                          {comment.sender}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {comment.createdAt
                            ? dayjs(comment.createdAt).format(
                                'MMM D, YYYY h:mm A'
                              )
                            : ''}
                        </span>
                      </div>
                      <p className="text-xs">{comment.comment}</p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
