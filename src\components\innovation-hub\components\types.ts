import * as z from 'zod';

// Schema for the idea submission form
export const ideaSchema = z.object({
  title: z.string().min(5, {
    message: 'Title must be at least 5 characters long.',
  }),
  description: z.string().min(20, {
    message: 'Description must be at least 20 characters long.',
  }),
});

export type IdeaFormValues = z.infer<typeof ideaSchema>;

// Schema for the comment submission form
export const commentSchema = z.object({
  comment: z
    .string()
    .min(1, { message: 'Comment cannot be empty.' })
    .max(280, { message: 'Comment is too long.' }),
});
export type CommentFormValues = z.infer<typeof commentSchema>;

// Re-export types from API
export type { Idea, IdeaComment, IdeaStatus } from '@/api/innovation-hub/data';

export const ideaStatuses = ['New', 'Under Review', 'Approved', 'Rejected', 'Implemented'] as const;

