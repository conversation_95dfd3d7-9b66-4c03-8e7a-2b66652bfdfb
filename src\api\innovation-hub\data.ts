import useSWR from 'swr';
import { myApi } from '../fetcher';
import { toast } from 'sonner';

// Types
export interface Idea {
  id: string;
  title: string;
  description: string;
  author: {
    id: string;
    fullName: string;
  };
  createdAt: string;
  updatedAt: string;
  likes: number;
  comments: IdeaComment[];
  status: IdeaStatus;
  isLikedByUser?: boolean;
}

export interface IdeaComment {
  id: string;
  ideaId: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  text: string;
  createdAt: string;
  updatedAt: string;
}

export type IdeaStatus = 'New' | 'Under Review' | 'Approved' | 'Rejected' | 'Implemented';

export interface IdeaFormValues {
  title: string;
  description: string;
  category?: string;
  tags?: string[];
}

export interface IdeasResponse {
  ideas: Idea[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface SearchParams {
  query?: string;
  status?: IdeaStatus;
  category?: string;
  page?: number;
  limit?: number;
  sortBy?: 'createdAt' | 'likes' | 'title';
  sortOrder?: 'asc' | 'desc';
}

// Get all ideas with search and pagination
export const GetIdeas = (params: SearchParams = {}) => {
  const searchParams = new URLSearchParams();
  
  if (params.query) searchParams.append('query', params.query);
  if (params.status) searchParams.append('status', params.status);
  if (params.category) searchParams.append('category', params.category);
  if (params.page) searchParams.append('page', params.page.toString());
  if (params.limit) searchParams.append('limit', params.limit.toString());
  if (params.sortBy) searchParams.append('sortBy', params.sortBy);
  if (params.sortOrder) searchParams.append('sortOrder', params.sortOrder);

  const queryString = searchParams.toString();
  const url = `/innovation-hub/ideas/list${queryString ? `?${queryString}` : ''}`;

  const { data, error, isLoading, mutate } = useSWR(url);

  return {
    ideas: data?.data?.ideas || [],
    total: data?.data?.total || 0,
    page: data?.data?.page || 1,
    limit: data?.data?.limit || 10,
    totalPages: data?.data?.totalPages || 0,
    isLoading,
    error,
    mutate,
  };
};

// Create a new idea
export const createIdea = async (ideaData: IdeaFormValues) => {
  try {
    const response = await myApi.post('/innovation-hub/ideas', ideaData);
    toast.success('Idea submitted successfully');
    return response.data;
  } catch (error) {
    console.error('Error creating idea:', error);
    throw error;
  }
};

// Like/Unlike an idea
export const toggleIdeaLike = async (ideaId: string) => {
  try {
    const response = await myApi.post(`/innovation-hub/ideas/${ideaId}/like`);
    return response.data;
  } catch (error) {
    console.error('Error toggling idea like:', error);
    throw error;
  }
};

// Update idea status (admin only)
export const updateIdeaStatus = async (ideaId: string, status: IdeaStatus) => {
  try {
    const response = await myApi.put(`/innovation-hub/ideas/${ideaId}/status`, { status });
    toast.success(`Idea status updated to "${status}"`);
    return response.data;
  } catch (error) {
    console.error('Error updating idea status:', error);
    throw error;
  }
};

// Add comment to an idea
export const addIdeaComment = async (ideaId: string, text: string) => {
  try {
    const response = await myApi.post(`/innovation-hub/ideas/${ideaId}/comments`, { text });
    toast.success('Comment added successfully');
    return response.data;
  } catch (error) {
    console.error('Error adding comment:', error);
    throw error;
  }
};

// Delete an idea
export const deleteIdea = async (ideaId: string) => {
  try {
    const response = await myApi.delete(`/innovation-hub/ideas/${ideaId}`);
    toast.success('Idea deleted successfully');
    return response.data;
  } catch (error) {
    console.error('Error deleting idea:', error);
    throw error;
  }
};

// Get innovation hub statistics
export const GetIdeaStats = () => {
  const { data, error, isLoading, mutate } = useSWR('/innovation-hub/stats');

  return {
    stats: data?.data || {
      totalIdeas: 0,
      approvedIdeas: 0,
      underReviewIdeas: 0,
      implementedIdeas: 0,
      rejectedIdeas: 0,
      newIdeas: 0,
    },
    isLoading,
    error,
    mutate,
  };
};