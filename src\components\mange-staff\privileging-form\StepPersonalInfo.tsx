'use client';

import { useFormContext } from 'react-hook-form';
import { Input } from '@/components/ui/input';

export default function StepPersonalInfo() {
  const { register } = useFormContext();

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Date of Birth
          </label>
          <Input type="date" {...register('dob')} />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Nationality
          </label>
          <Input type="text" {...register('nationality')} />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Ethnicity
          </label>
          <Input type="text" {...register('ethnicity')} />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">Sex</label>
          <select
            {...register('sex')}
            className="flex h-10 w-full rounded-sm border border-input bg-transparent px-3 py-1 text-sm shadow-xs"
          >
            <option value="">Select</option>
            <option>Male</option>
            <option>Female</option>
          </select>
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">Gender</label>
          <Input type="text" {...register('gender')} />
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Home Details
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input placeholder="Address" {...register('home.address')} />
          <Input placeholder="Postcode" {...register('home.postcode')} />
          <Input placeholder="Telephone" {...register('home.telephone')} />
          <Input
            type="number"
            placeholder="Mileage from Hospital"
            {...register('home.mileage')}
          />
          <Input
            placeholder="Travel Time to Hospital"
            {...register('home.travelTime')}
          />
          <Input type="email" placeholder="Email" {...register('home.email')} />
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Next of Kin
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Input placeholder="Name" {...register('nextOfKin.name')} />
          <Input placeholder="Telephone" {...register('nextOfKin.telephone')} />
          <Input
            placeholder="Relationship"
            {...register('nextOfKin.relationship')}
          />
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Secretary Details
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Input placeholder="Name" {...register('secretary.name')} />
          <Input placeholder="Telephone" {...register('secretary.telephone')} />
          <Input
            placeholder="NHS/Private/Both"
            {...register('secretary.type')}
          />
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Private Practice Address
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input placeholder="Address" {...register('practice.address')} />
          <Input placeholder="Postcode" {...register('practice.postcode')} />
          <Input placeholder="Telephone" {...register('practice.telephone')} />
          <Input
            type="email"
            placeholder="Email"
            {...register('practice.email')}
          />
          <Input
            placeholder="Website"
            {...register('practice.website')}
            className="md:col-span-2"
          />
        </div>
      </div>
    </div>
  );
}
