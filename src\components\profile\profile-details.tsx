'use client';

import React, { useState } from 'react';
import { Modal } from '@/components/common/modal';
import { UserProps } from './types';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  User,
  Calendar,
  Mail,
  Shield,
  Clock,
  Phone,
  MapPin,
  Building,
  Wallet,
  ClipboardCheck,
  CreditCard,
  Copy,
  FileText,
} from 'lucide-react';
import dayjs from 'dayjs';
import { formatRoleNames, numberFormat } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { WithdrawModal } from './withdraw-modal';
import { PractisingPrivilegesModal } from './practising-privileges-modal';

interface ProfileDetailsProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  user: UserProps;
}

const ProfileDetails: React.FC<ProfileDetailsProps> = ({
  open,
  setOpen,
  user,
}) => {
  const [withdrawModalOpen, setWithdrawModalOpen] = useState(false);
  const [practisingPrivilegesOpen, setPractisingPrivilegesOpen] = useState(false);

  if (!user) return null;

  const formatDate = (date: Date | string | undefined) => {
    if (!date) return 'N/A';
    return dayjs(date).format('MMM DD, YYYY hh:mm A');
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Profile Details"
      description="Your complete account and profile information"
      size="lg"
    >
      <Tabs defaultValue="personal" className="w-full">
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="personal" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Personal & Professional
          </TabsTrigger>
          <TabsTrigger value="account" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Account & Financial
          </TabsTrigger>
        </TabsList>

        <TabsContent value="personal" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Personal Information Column */}
            <div className="space-y-4 text-sm">
              <h3 className="text-lg font-semibold text-foreground mb-3">
                Personal Information
              </h3>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Full Name
                </span>
                <span className="font-semibold">{user.fullName || 'N/A'}</span>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Email Address
                </span>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span>{user.email || 'N/A'}</span>
                </div>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="font-medium text-muted-foreground">
                  Phone Number
                </span>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span>{user.phoneNumber || 'N/A'}</span>
                </div>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="font-medium text-muted-foreground">
                  Role Permissions
                </span>
                <Badge variant="outline" className="w-fit">
                  {formatRoleNames(user.roles)}
                </Badge>
              </div>

              {user.location && (
                <div className="flex flex-col space-y-1">
                  <span className="text-sm font-medium text-muted-foreground">
                    Location
                  </span>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>
                      {user.location.name}, {user.location.region}
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Professional Information Column */}
            <div className="space-y-4 text-sm">
              <h3 className="text-lg font-semibold text-foreground mb-3">
                Professional Information
              </h3>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Staff ID
                </span>
                <span className="font-medium">
                  {user.staffCode || user.staffId || 'N/A'}
                </span>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Designation/Role
                </span>
                <span>{user.role || 'N/A'}</span>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Staff Type
                </span>
                <span>{user.type || 'N/A'}</span>
              </div>

              {user.department && (
                <div className="flex flex-col space-y-1">
                  <span className="text-sm font-medium text-muted-foreground">
                    Department
                  </span>
                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4 text-muted-foreground" />
                    <span>{user.department.name}</span>
                  </div>
                </div>
              )}

              {user.unitId && user.unit && (
                <div className="flex flex-col space-y-1">
                  <span className="text-sm font-medium text-muted-foreground">
                    Unit
                  </span>
                  <span>{user.unit.name}</span>
                </div>
              )}

              {user.doctorProfile && (
                <>
                  <div className="flex flex-col space-y-1">
                    <span className="text-sm font-medium text-muted-foreground">
                      Consultant Status
                    </span>
                    <span>
                      {user.doctorProfile.isConsultant
                        ? user.doctorProfile.isVisitingConsultant
                          ? 'Visiting Consultant'
                          : 'Consultant'
                        : 'Doctor'}
                    </span>
                  </div>
                  {user.doctorProfile.specialty && (
                    <div className="flex flex-col space-y-1">
                      <span className="text-sm font-medium text-muted-foreground">
                        Specialty
                      </span>
                      <span>{user.doctorProfile.specialty.name}</span>
                    </div>
                  )}
                  {user.doctorProfile.isConsultant && (
                    <div className="mt-6 pt-4 border-t">
                      <Button
                        onClick={() => setPractisingPrivilegesOpen(true)}
                        className="w-full"
                      >
                        <FileText className="w-4 h-4 mr-2" />
                        Practising Privileges
                      </Button>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="account" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Account Information Column */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-foreground mb-3">
                Account Information
              </h3>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Account Status
                </span>
                <Badge
                  variant={user.isActive ? 'success' : 'destructive'}
                  className="w-fit"
                >
                  {user.isActive !== undefined
                    ? user.isActive
                      ? 'Active'
                      : 'Inactive'
                    : 'Unknown'}
                </Badge>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Account Locked
                </span>
                <Badge
                  variant={user.locked ? 'destructive' : 'success'}
                  className="w-fit"
                >
                  {user.locked ? 'Locked' : 'Unlocked'}
                </Badge>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Account Created
                </span>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>{formatDate(user.createdAt)}</span>
                </div>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Last Login
                </span>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>{formatDate(user.lastLogin)}</span>
                </div>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Last Password Reset
                </span>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>{formatDate(user.dateResetPassword)}</span>
                </div>
              </div>
            </div>

            {/* Financial Information Column */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-foreground mb-3">
                Financial Information
              </h3>
              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Wallet Balance{' '}
                  <Button
                    className="text-xs"
                    size="sm"
                    disabled={!user?.wallet || Number(user?.wallet) === 0}
                    onClick={() => setWithdrawModalOpen(true)}
                  >
                    Withdraw/Transfer
                  </Button>
                </span>
                <div className="flex items-center gap-2">
                  <Wallet className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold text-green-600">
                    {user?.wallet ? numberFormat(user.wallet) : '₦0.00'}
                  </span>
                </div>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Credit Limit
                </span>
                <div className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold">
                    {user?.creditLimit
                      ? numberFormat(user.creditLimit)
                      : '₦0.00'}
                  </span>
                </div>
              </div>
              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Monthly Credit Used
                </span>
                <div className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold text-orange-600">
                    {user?.monthlyCreditUsed
                      ? numberFormat(user.monthlyCreditUsed)
                      : '₦0.00'}
                  </span>
                </div>
              </div>
              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Meal Voucher
                </span>
                <span className="font-semibold text-blue-600">
                  {user?.mealVoucher ? numberFormat(user.mealVoucher) : '₦0.00'}
                </span>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Total Earnings
                </span>
                <span className="font-semibold">
                  {user?.total ? numberFormat(user.total) : '₦0.00'}
                </span>
              </div>

              {user.referralCode && (
                <div className="flex flex-col space-y-1">
                  <span className="text-sm font-medium text-muted-foreground">
                    Referral Code
                  </span>
                  <div className="flex items-center gap-2">
                    <ClipboardCheck className="h-4 w-4 text-muted-foreground" />
                    <Badge variant="outline" className="px-3 py-1 text-sm">
                      {user.referralCode.code}
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        navigator.clipboard.writeText(
                          user.referralCode?.code || ''
                        );
                        toast.success('Referral code copied to clipboard');
                      }}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              )}

              {user.codeUsage !== undefined && (
                <div className="flex flex-col space-y-1">
                  <span className="text-sm font-medium text-muted-foreground">
                    Referral Code Usage
                  </span>
                  <span className="font-medium">{user.codeUsage} times</span>
                </div>
              )}
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <WithdrawModal
        open={withdrawModalOpen}
        setOpen={setWithdrawModalOpen}
        walletBalance={Number(user?.wallet) || 0}
        setProfileOpen={setOpen}
      />

      <PractisingPrivilegesModal
        open={practisingPrivilegesOpen}
        setOpen={setPractisingPrivilegesOpen}
        user={user}
      />
    </Modal>
  );
};

export default ProfileDetails;
