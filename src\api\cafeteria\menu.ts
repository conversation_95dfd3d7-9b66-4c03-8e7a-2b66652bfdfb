import useSWR from 'swr';
import { useAuthSWR } from '../useAuthSWR';

export const GetAllMenu = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/cafeteria/menu/list?${qs.toString()}`
  );

  return {
    menu: data,
    menuLoading: isLoading,
    mutate: mutate,
  };
};

export const GetAllMenuCat = () => {
  const { data, isLoading } = useAuthSWR(`/cafeteria/menu-category/list`);

  return {
    menuCat: data,
    menuCategoryLoading: isLoading,
  };
};

//Orders

export const GetAllOrders = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/cafeteria/orders/list?${qs.toString()}`
  );

  return {
    orders: data,
    orderLoading: isLoading,
    mutate: mutate,
  };
};

export const GetGeneralOrders = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/cafeteria/orders/general/list?${qs.toString()}`
  );

  return {
    orders: data,
    orderLoading: isLoading,
    mutate: mutate,
  };
};

export const GetSpecialOrders = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/cafeteria/orders/list-special-order?${qs.toString()}`
  );

  return {
    specialOrders: data,
    specialLoading: isLoading,
    mutate: mutate,
  };
};

export const GetStaffSpecialOrders = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/cafeteria/orders/staff-special-order?${qs.toString()}`
  );

  return {
    specialOrders: data,
    specialLoading: isLoading,
    mutate: mutate,
  };
};
