import * as z from 'zod';

export const changePasswordSchema = z
  .object({
    currentPassword: z
      .string()
      .min(6, { message: 'Current password must be at least 6 characters' }),
    newPassword: z
      .string()
      .min(6, { message: 'New password must be at least 6 characters' })
      .max(15, { message: 'New password must be less than 15 characters' }),
    // .refine((password) => /[A-Z]/.test(password), {
    //   message: 'Password must contain at least one uppercase letter',
    // })
    // .refine((password) => /[a-z]/.test(password), {
    //   message: 'Password must contain at least one lowercase letter',
    // })
    // .refine((password) => /[0-9]/.test(password), {
    //   message: 'Password must contain at least one number',
    // })
    // .refine((password) => /[^A-Za-z0-9]/.test(password), {
    //   message: 'Password must contain at least one special character',
    // }),
    confirmPassword: z
      .string()
      .min(6, { message: 'Confirm password must be at least 6 characters' }),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });

export type ChangePasswordFormValues = z.infer<typeof changePasswordSchema>;
