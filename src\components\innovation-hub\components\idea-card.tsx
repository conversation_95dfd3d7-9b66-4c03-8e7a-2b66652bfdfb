import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  Card<PERSON>eader,
  CardTitle,
} from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Edit,
  MessageCircle,
  MoreHorizontal,
  Share2,
  ThumbsUp,
  Trash2,
} from 'lucide-react';
import { toast } from 'sonner';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Idea, IdeaStatus, ideaStatuses } from '../components/types';

// Status Badge Component
const StatusBadge = ({ status }: { status: IdeaStatus }) => {
  const statusStyles: Record<IdeaStatus, string> = {
    New: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    'Under Review':
      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    Approved:
      'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    Rejected:
      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    Implemented:
      'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
  };

  return (
    <Badge className={`${statusStyles[status]} border-none`}>{status}</Badge>
  );
};

// Idea Card Component
export const IdeaCard = ({
  idea,
  onLike,
  canEdit,
  onDelete,
  onStatusUpdate,
  onCommentClick,
}: {
  idea: Idea;
  onLike: (id: string) => void;
  canEdit: boolean;
  onDelete: (id: string) => void;
  onStatusUpdate: (id: string, status: IdeaStatus) => void;
  onCommentClick: (idea: Idea) => void;
}) => {
  const handleShare = () => {
    navigator.clipboard.writeText(
      `Check out this idea: "${idea.title}" on our Innovation Hub!`
    );
    toast.success('Link copied to clipboard!');
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Avatar>
              {/* <AvatarImage src={idea.author.avatar} alt={idea.author.name} /> */}
              <AvatarFallback>
                {idea.author.fullName
                  .split(' ')
                  .map((n) => n[0])
                  .join('')}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="font-semibold">{idea.author.fullName}</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {idea.createdAt}
              </p>
            </div>
          </div>
          {canEdit && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuSub>
                  <DropdownMenuSubTrigger>
                    <Edit className="mr-2 h-4 w-4" />
                    <span>Update Status</span>
                  </DropdownMenuSubTrigger>
                  <DropdownMenuPortal>
                    <DropdownMenuSubContent>
                      {ideaStatuses.map((status) => (
                        <DropdownMenuItem
                          key={status}
                          onClick={() => onStatusUpdate(idea.id, status)}
                        >
                          {status}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuSubContent>
                  </DropdownMenuPortal>
                </DropdownMenuSub>
                <DropdownMenuItem
                  onClick={() => onDelete(idea.id)}
                  className="text-red-500 focus:text-red-500"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  <span>Delete</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between mb-2">
          <CardTitle className="text-xl">{idea.title}</CardTitle>
          <StatusBadge status={idea.status} />
        </div>
        <CardDescription>{idea.description}</CardDescription>
      </CardContent>
      <CardFooter className="flex justify-between items-center">
        <div className="flex space-x-4 text-gray-600 dark:text-gray-400">
          <Button variant="ghost" size="sm" onClick={() => onLike(idea.id)}>
            <ThumbsUp className="h-4 w-4 mr-2" />
            {idea.likes}
          </Button>
          <Button variant="ghost" size="sm" onClick={() => onCommentClick(idea)}>
            <MessageCircle className="h-4 w-4 mr-2" />
            {idea.comments.length}
          </Button>
        </div>
        <Button variant="ghost" size="sm" onClick={handleShare}>
          <Share2 className="h-4 w-4 mr-2" />
          Share
        </Button>
      </CardFooter>
    </Card>
  );
};

