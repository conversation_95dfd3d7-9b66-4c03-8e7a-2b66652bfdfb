import { Modal } from '@/components/common/modal';
import React, { useState } from 'react';
import { ModalProps } from '../../../types';
import { currencyFormat, cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { GetProfile } from '@/api/staff';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { Loader2, User, CreditCard } from 'lucide-react';
import dayjs from 'dayjs';
import { Input } from '@/components/ui/input';
import { Tag } from 'lucide-react';

// Tab interface
interface TabProps {
  label: string;
  icon: React.ReactNode;
  content: React.ReactNode;
}

const Details: React.FC<ModalProps> = ({ setOpen, open, mutate, data }) => {
  const { profile } = GetProfile();
  const [inputValue, setInputValue] = useState('');
  const [voucherCode, setVoucherCode] = useState('');
  const [isVoucherLoading, setIsVoucherLoading] = useState(false);
  const [voucherApplied, setVoucherApplied] = useState(false);
  const [voucherAmount, setVoucherAmount] = useState(0);
  const [voucherValue, setVoucherValue] = useState(0);
    const [isDisabled, setIsDisabled] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);


  const calculateAmount = (amount: number, voucherAmount: number) => {
    return Number(amount) - Number(voucherAmount);
  };

  interface InputChangeEvent extends React.ChangeEvent<HTMLInputElement> {}

  const handleInputChange = (event: InputChangeEvent) => {
    setInputValue(event.target.value);
    setError('');
  };

  const handleSubmit = async () => {
    try {
      setIsLoading(true);
      const res = await myApi.patch('/booking/update-booking', {
        bookingId: data.id,
        updatedBy: profile.data.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.message);
        setOpen(false);
        if (mutate) {
          mutate();
        }
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  const handleVoucherSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!inputValue.trim()) {
      setError('Please enter a voucher code');
      return;
    }
    try {
      setIsVoucherLoading(true);
      const res = await myApi.post('/booking/verify-price-modifier', {
        id: data?.packageLocationId,
        modifierCode: inputValue,
      });
      setIsVoucherLoading(false);
      if (res.status === 200) {
        const { amount, percentage, modifierCode } = res.data.data.modifier;
        let discountValue = 0;

        if (percentage) {
          discountValue = (Number(percentage) / 100) * data.amount;
        } else {
          discountValue = Number(amount);
        }

        setVoucherValue(discountValue);
        setVoucherAmount(calculateAmount(data.amount, discountValue));
        setIsDisabled(true);
        setVoucherApplied(true);
        setError('');
        setVoucherCode(modifierCode);
      }
    } catch (error) {
      setIsVoucherLoading(false);
      setError('Invalid voucher code');
    }
  };

  const totalPayableAmount = voucherAmount > 0 ? voucherAmount : data.amount;

  const handleOnsiteSuccess = async () => {
    try {
      setIsLoading(true);
      const res = await myApi.patch(`/booking/complete-booking`, {
        transactionType: 'Onsite',
        bookingStatus: 'COMPLETED',
        bookingId: data.id,
        amount: totalPayableAmount,
        voucherCode: voucherCode,
        voucherDiscount: voucherValue,
        transactionReference: data.bookingRef,
      });
      if (res.status === 200) {
        toast.success(res.data.message);
        setOpen(false);
        if (mutate) {
          mutate();
        }
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  // Tab content components
  const PatientInfo = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
          Personal Information
        </h3>
        <div className="grid sm:grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-gray-500 dark:text-gray-400">Full Name/UHID</p>
            <p className="font-medium">
              {(data.user?.title ? data.user.title + ' ' : '') +
                (data.user?.firstName ? data.user.firstName + ' ' : '') +
                (data.user?.lastName || '') || data.user.uhid}
            </p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Email</p>
            <p className="font-medium">{data.user?.emailAddress || 'N/A'}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Phone</p>
            <p className="font-medium">{data.user?.phoneNumber || 'N/A'}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Gender</p>
            <p className="font-medium">{data.user?.gender || 'N/A'}</p>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
          Booking Information
        </h3>
        <div className="grid sm:grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-gray-500 dark:text-gray-400">Package</p>
            <p className="font-medium">{data.packages?.name || 'N/A'}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Booking Status</p>
            <p className="font-medium">{data.bookingStatus || 'N/A'}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Booking Date</p>
            <p className="font-medium">
              {dayjs(data.createdAt).format('MMMM D, YYYY')}
            </p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Location</p>
            <p className="font-medium">{data.location || 'N/A'}</p>
          </div>
        </div>
      </div>
    </div>
  );

  const PaymentInfo = () => (
    <div className="grid sm:grid-cols-2 gap-4 text-sm">
      <div>
        <p className="text-gray-500 dark:text-gray-400">Package Amount</p>
        <p className="font-medium">{`NGN ${currencyFormat(data.amount)}`}</p>
      </div>
      <div>
        <p className="text-gray-500 dark:text-gray-400">Amount Paid</p>
        <p className="font-medium">
          {data.totalAmount
            ? `NGN ${currencyFormat(data.totalAmount)}`
            : 'NGN 0'}
        </p>
      </div>
      <div>
        <p className="text-gray-500 dark:text-gray-400">Referral Code</p>
        <p className="font-medium">
          {data.referralCode ? data.referralCode : 'N/A'}
        </p>
      </div>
      <div>
        <p className="text-gray-500 dark:text-gray-400">Voucher Code</p>
        <p className="font-medium">
          {data.voucherCode ? data.voucherCode : 'N/A'}
        </p>
      </div>
      <div>
        <p className="text-gray-500 dark:text-gray-400">Voucher Discount</p>
        <p className="font-medium">{data.voucherDiscount || 'N/A'}</p>
      </div>
    </div>
  );

  // Define tabs
  const tabs: TabProps[] = [
    {
      label: 'Patient',
      icon: <User className="w-4 h-4" />,
      content: <PatientInfo />,
    },
    {
      label: 'Payment',
      icon: <CreditCard className="w-4 h-4" />,
      content: <PaymentInfo />,
    },
  ];

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Booking Details"
      description={`Booking with reference ${data.bookingRef}`}
    >
      <>
        {/* Tabs */}
        <div className="flex border-b mb-4">
          {tabs.map((tab, index) => (
            <button
              key={index}
              className={cn(
                'flex items-center gap-1 px-4 py-2 text-sm font-medium',
                activeTab === index
                  ? 'border-b-2 border-primary text-primary'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              )}
              onClick={() => setActiveTab(index)}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </div>

        {/* Tab content */}
        <div className="py-2">{tabs[activeTab].content}</div>

        {data && data.updatedBy && (
          <p className="text-xs mt-4 text-gray-500 dark:text-gray-400">
            Last updated by: {data.updatedBy} -{' '}
            {dayjs(data.updatedAt).format('MMMM D, YYYY h:mm A')}
          </p>
        )}

        {data && data.bookingStatus === 'PENDING' && (
          <div className="flex justify-center mt-4">
            <Button
              disabled={isLoading}
              className="cursor-pointer"
              onClick={handleSubmit}
            >
              {isLoading ? (
                <Loader2 className="animate-spin mr-2 h-4 w-4" />
              ) : (
                'Mark as treated'
              )}
            </Button>
          </div>
        )}
        {data && data.bookingStatus === 'DRAFT' && (
          <div className="p-2">
            {Number(data?.amount) > 0 ? (
              <div className="rounded-lg bg-muted/50 p-3">
                <div className="mb-2 flex items-center">
                  <Tag className="mr-2 h-4 w-4 text-primary" />
                  <span className="font-medium">Apply Voucher Code</span>
                </div>
                <p className="text-xs mb-2 font-semibold">
                  If the patient used voucher code, Please enter the correct
                  code here and click apply before completing the booking.
                </p>
                <div className="flex items-center space-x-2">
                  <Input
                   disabled={isDisabled}
                    type="text"
                    value={inputValue}
                    onChange={handleInputChange}
                    className="border-input focus-visible:ring-primary"
                    placeholder="Enter voucher code"
                  />
                  <Button
                    type="button"
                    onClick={handleVoucherSubmit}
disabled={isVoucherLoading || isDisabled}                    variant="secondary"
                    className="whitespace-nowrap"
                  >
                    {isVoucherLoading ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : null}
                    {isVoucherLoading ? 'Verifying...' : 'Apply'}
                  </Button>
                </div>
                {error && (
                  <p className="mt-2 text-xs font-medium text-destructive">
                    {error}
                  </p>
                )}
                {voucherApplied && (
                  <p className="mt-2 text-xs font-medium text-green-600">
                    Voucher successfully applied!
                  </p>
                )}
              </div>
            ) : null}
            <div className="flex justify-center mt-4">
              <Button
                disabled={isLoading}
                className="cursor-pointer"
                onClick={handleOnsiteSuccess}
              >
                {isLoading ? (
                  <Loader2 className="animate-spin mr-2 h-4 w-4" />
                ) : (
                  'Complete booking as onsite'
                )}
              </Button>
            </div>
          </div>
        )}
      </>
    </Modal>
  );
};

export default Details;