'use client';

import { useState, useEffect } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ChevronLeft, ChevronRight, FileText, ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { GetProfile } from '@/api/staff';
import StepPersonalInfo from './StepPersonalInfo';
import StepConsultancy from './StepConsultancy';
import StepPrivileges from './StepPrivileges';
import StepReferees from './StepReferees';
import StepAdditionalInfo from './StepAdditionalInfo';
import StepCover from './StepCover';
import StepIndemnity from './StepIndemnity';
import StepReview from './StepReview';

const steps = [
  { title: 'Personal Information', component: StepPersonalInfo },
  { title: 'Consultancy Details', component: StepConsultancy },
  { title: 'Privileges', component: StepPrivileges },
  { title: 'Referees', component: StepReferees },
  { title: 'Additional Information', component: StepAdditionalInfo },
  { title: 'Cover & Subcontracting', component: StepCover },
  { title: 'Indemnity & Declarations', component: StepIndemnity },
  { title: 'Review & Submit', component: StepReview },
];

export default function PrivilegingForm() {
  const [currentStep, setCurrentStep] = useState(0);
  const methods = useForm();
  const router = useRouter();
  const { profile: user } = GetProfile();

  useEffect(() => {
    if (user?.data && !user.data.doctorProfile?.isConsultant) {
      router.push('/forbidden');
    }
  }, [user, router]);

  const isStepValid = (stepIndex: number) => {
    const data = methods.getValues();
    switch (stepIndex) {
      case 0:
        return data.dob && data.nationality;
      case 1:
        return data.consultancy?.specialistRegisterDate;
      case 2:
        return data.privileges && Object.keys(data.privileges).length > 0;
      case 3:
        return data.referees?.[1]?.name && data.referees?.[2]?.name;
      case 4:
        return data.additional?.lastAppraisal;
      case 5:
        return data.cover?.support;
      case 6:
        return data.indemnity?.member && data.signature;
      default:
        return true;
    }
  };

  const nextStep = () => {
    if (currentStep < steps.length - 1 && isStepValid(currentStep)) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const onSubmit = (data: any) => {
    console.log('Form submitted:', data);
    router.push('/dashboard');
  };

  if (!user?.data?.doctorProfile?.isConsultant) {
    return null;
  }

  const CurrentStepComponent = steps[currentStep].component;

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="mb-8">
          <Button
            variant="ghost"
            onClick={() => window.history.back()}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold text-foreground">
            Privileging Application Form
          </h1>
          <p className="text-muted-foreground mt-2">
            Complete your privileging application
          </p>
        </div>

        <FormProvider {...methods}>
          <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-8">
            {/* Progress Indicator */}
            <div className="flex items-center justify-between mb-8">
              {steps.map((step, index) => (
                <div key={index} className="flex items-center">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                      index <= currentStep
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted text-muted-foreground'
                    }`}
                  >
                    {index + 1}
                  </div>
                  {index < steps.length - 1 && (
                    <div
                      className={`flex-1 h-0.5 mx-4 ${
                        index < currentStep ? 'bg-primary' : 'bg-muted'
                      }`}
                    />
                  )}
                </div>
              ))}
            </div>

            {/* Step Title */}
            <div className="text-center mb-8">
              <h2 className="text-2xl font-semibold text-foreground">
                {steps[currentStep].title}
              </h2>
              <p className="text-muted-foreground mt-2">
                Step {currentStep + 1} of {steps.length}
              </p>
            </div>

            {/* Step Content */}
            <Card>
              <CardContent className="p-8">
                <CurrentStepComponent />
              </CardContent>
            </Card>

            {/* Navigation Buttons */}
            <div className="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={prevStep}
                disabled={currentStep === 0}
                size="lg"
              >
                <ChevronLeft className="w-4 h-4 mr-2" />
                Previous
              </Button>

              {currentStep === steps.length - 1 ? (
                <Button type="submit" size="lg">
                  <FileText className="w-4 h-4 mr-2" />
                  Submit Application
                </Button>
              ) : (
                <Button
                  type="button"
                  onClick={nextStep}
                  size="lg"
                  disabled={!isStepValid(currentStep)}
                >
                  Next
                  <ChevronRight className="w-4 h-4 ml-2" />
                </Button>
              )}
            </div>
          </form>
        </FormProvider>
      </div>
    </div>
  );
}
