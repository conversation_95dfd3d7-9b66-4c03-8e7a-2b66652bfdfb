// This is a utility script to create the PDF template file
// Run this once to generate the template in the public folder

import { generatePractisingPrivilegesTemplate } from './pdf-template-generator';
import fs from 'fs';
import path from 'path';

export const createPdfTemplateFile = () => {
  try {
    const pdfBytes = generatePractisingPrivilegesTemplate();
    const publicPath = path.join(process.cwd(), 'public', 'Practising Privileges.pdf');
    
    fs.writeFileSync(publicPath, pdfBytes);
    console.log('PDF template created successfully at:', publicPath);
  } catch (error) {
    console.error('Error creating PDF template:', error);
  }
};

// Uncomment the line below and run this file to create the template
// createPdfTemplateFile();
