'use client';

import { useFormContext } from 'react-hook-form';
import { Input } from '@/components/ui/input';

export default function StepCover() {
  const { register } = useFormContext();

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Support structure for cover
          </label>
          <p className="text-xs text-muted-foreground">
            Describe arrangements for anaesthesia, patients returning to
            theatre, etc.
          </p>
          <textarea
            {...register('cover.support')}
            className="flex min-h-[100px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Subcontracted third-party arrangements
          </label>
          <textarea
            {...register('cover.subcontract')}
            className="flex min-h-[100px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
          />
        </div>
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium text-foreground">
          Tax Registration Number
        </label>
        <Input
          placeholder="Enter tax registration number"
          {...register('cover.taxNo')}
        />
      </div>
    </div>
  );
}
