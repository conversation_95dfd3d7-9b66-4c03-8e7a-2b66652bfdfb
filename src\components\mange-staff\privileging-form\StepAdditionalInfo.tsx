'use client';

import { useFormContext } from 'react-hook-form';
import { Input } from '@/components/ui/input';

export default function StepAdditionalInfo() {
  const { register } = useFormContext();

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Date of last medical appraisal
          </label>
          <Input type="date" {...register('additional.lastAppraisal')} />
        </div>
        <Input
          placeholder="Responsible Officer Contact"
          {...register('additional.responsibleOfficer')}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Awarded CCST/CCT?
          </label>
          <select
            {...register('additional.ccst')}
            className="flex h-10 w-full rounded-sm border border-input bg-transparent px-3 py-1 text-sm shadow-xs"
          >
            <option value="">Select</option>
            <option>Yes</option>
            <option>No</option>
          </select>
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Date of Award
          </label>
          <Input type="date" {...register('additional.ccstDate')} />
        </div>
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium text-foreground">
          Meet CME Requirements?
        </label>
        <select
          {...register('additional.cme')}
          className="flex h-10 w-full rounded-sm border border-input bg-transparent px-3 py-1 text-sm shadow-xs"
        >
          <option value="">Select</option>
          <option>Yes</option>
          <option>No</option>
        </select>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Hospital Affiliations
        </h3>
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Other Private Hospitals (Granted)
            </label>
            <textarea
              {...register('additional.hospitalsGranted')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Other Private Hospitals (Refused)
            </label>
            <textarea
              {...register('additional.hospitalsRefused')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Professional Details
        </h3>
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Scope of Practice
            </label>
            <textarea
              {...register('additional.scope')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Sub-specialties & training
            </label>
            <textarea
              {...register('additional.subSpecialties')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Professional Organisations
            </label>
            <textarea
              {...register('additional.organisations')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
