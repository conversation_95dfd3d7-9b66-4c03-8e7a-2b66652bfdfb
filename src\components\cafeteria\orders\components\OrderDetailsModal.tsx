'use client';

import { useState, useEffect } from 'react';
import { Modal } from '@/components/common/modal';
import { currencyFormat } from '@/lib/utils';
import dayjs from 'dayjs';
import {
  hasPermission,
  profileDetails,
  PERMISSIONS,
} from '@/lib/types/permissions';
import { Button } from '@/components/ui/button';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { AlertTriangle } from 'lucide-react';

interface OrderDetailsModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  selectedOrder: any;
  mutate?: () => void;
}

export default function OrderDetailsModal({
  open,
  setOpen,
  selectedOrder,
  mutate,
}: OrderDetailsModalProps) {
  const canApprove = hasPermission(PERMISSIONS.CAFETERIA_SPECIAL_APPROVE);
  const canManage = hasPermission(PERMISSIONS.CAFETERIA_ORDERS_MANAGE);
  const [isLoading, setIsLoading] = useState(false);
  const [declined, setDeclined] = useState(false);
  const [comment, setComment] = useState('');
  const [editingQuantities, setEditingQuantities] = useState(false);
  const [receivedItems, setReceivedItems] = useState<any[]>([]);
  const [editingReturns, setEditingReturns] = useState(false);
  const [returnItems, setReturnItems] = useState<any[]>([]);
  const [returnedItems, setReturnedItems] = useState<any[]>([]);
  const [timeLeft, setTimeLeft] = useState('');
  const [returnReason, setReturnReason] = useState('');

  const { fullName, id: profileId } = profileDetails();

  const handleUpdateOrder = async (value: string) => {
    const payload: any = {
      orderId: selectedOrder.id,
      status: value,
    };

    // Add specific fields based on the action
    if (value === 'APPROVED') {
      payload.approvedBy = fullName;
    } else if (value === 'DECLINED') {
      payload.declinedBy = fullName;
      payload.comment = comment;
    } else if (value === 'DELIVERED') {
      payload.deliveredBy = fullName;
    }
    try {
      setIsLoading(true);
      const res = await myApi.patch(
        '/cafeteria/orders/update-special-order',
        payload
      );
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.message);
        setOpen(false);
        setDeclined(false);
        setComment('');
        if (mutate) {
          mutate();
        }
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  const handleMarkReceived = async () => {
    const totalAmount = receivedItems.reduce(
      (sum, item) => sum + item.quantity * item.unitPrice,
      0
    );

    try {
      setIsLoading(true);
      const res = await myApi.patch('/cafeteria/orders/receive-special-order', {
        orderId: selectedOrder.id,
        receivedBy: fullName,
        receivedItems,
        totalAmount,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.message);
        setOpen(false);
        setEditingQuantities(false);
        setReceivedItems([]);
        if (mutate) {
          mutate();
        }
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  const handleMarkReturned = async () => {
    const totalAmount = returnItems.reduce(
      (sum, item) => sum + item.quantity * item.unitPrice,
      0
    );

    try {
      setIsLoading(true);
      const res = await myApi.patch('/cafeteria/orders/return-special-order', {
        orderId: selectedOrder.id,
        returnedBy: fullName,
        returnedItems: returnItems,
        totalAmount,
        reason: returnReason,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.message);
        setOpen(false);
        setEditingReturns(false);
        setReturnItems([]);
        setReturnReason('');
        if (mutate) {
          mutate();
        }
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  const initializeReceivedItems = () => {
    const items = selectedOrder.orderItems.map((item: any) => ({
      id: item.id,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
    }));
    setReceivedItems(items);
    setEditingQuantities(true);
  };

  const updateReceivedQuantity = (itemId: number, quantity: number) => {
    setReceivedItems((prev) =>
      prev.map((item) => (item.id === itemId ? { ...item, quantity } : item))
    );
  };

  const initializeReturnItems = () => {
    const items = selectedOrder.orderItems.map((item: any) => ({
      id: item.id,
      menuItemId: item.menuItemId,
      quantity: item.receivedQuantity || 0,
      unitPrice: item.unitPrice,
    }));
    setReturnItems(items);
    setEditingReturns(true);
  };

  const updateReturnQuantity = (itemId: number, quantity: number) => {
    setReturnItems((prev) =>
      prev.map((item) => (item.id === itemId ? { ...item, quantity } : item))
    );
  };

  const isReturnWindowOpen = () => {
    if (!selectedOrder.receivedAt) return false;
    const receivedTime = dayjs(selectedOrder.receivedAt);
    const now = dayjs();
    return now.diff(receivedTime, 'minute') < 60;
  };

  const hasReturnedItems = () => {
    return selectedOrder?.returnedItems && selectedOrder.returnedItems.length > 0;
  };

  const calculateTimeLeft = () => {
    if (!selectedOrder.receivedAt) return '';
    const receivedTime = dayjs(selectedOrder.receivedAt);
    const deadline = receivedTime.add(1, 'hour');
    const now = dayjs();
    const diff = deadline.diff(now);

    if (diff <= 0) return 'Expired';

    const minutes = Math.floor(diff / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  useEffect(() => {
    if (selectedOrder?.status === 'RECEIVED' && isReturnWindowOpen()) {
      const timer = setInterval(() => {
        setTimeLeft(calculateTimeLeft());
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [selectedOrder]);

  useEffect(() => {
    if (selectedOrder?.returnedItems) {
      setReturnedItems(selectedOrder.returnedItems);
    }
  }, [selectedOrder]);

  const handleCloseModal = (open: boolean) => {
    setOpen(open);
    if (!open) {
      setEditingQuantities(false);
      setReceivedItems([]);
      setEditingReturns(false);
      setReturnItems([]);
      setReturnReason('');
    }
  };

  return (
    <Modal
      open={open}
      setOpen={handleCloseModal}
      title={`Order Details - ${selectedOrder?.orderNumber || 'N/A'}`}
      description="View complete order information and items"
      size="lg"
    >
      {selectedOrder && selectedOrder.comment && (
        <div className="py-2">
          <div className="p-2 border-2 border-destructive-foreground rounded-md">
            <p className="text-sm font-medium text-gray-600">
              <span>
                <AlertTriangle className="inline-block w-5 h-5 text-red-500 mr-2" />
              </span>
              Comment
            </p>
            <p className="text-sm">{selectedOrder.comment}</p>
          </div>
        </div>
      )}
      {selectedOrder && (
        <div className="space-y-4">
          {selectedOrder.status === 'PENDING' && canApprove && (
            <>
              <div className="flex gap-2">
                <Button
                  disabled={declined || isLoading}
                  onClick={() => handleUpdateOrder('APPROVED')}
                  size="sm"
                >
                  Approve
                </Button>
                <Button
                  disabled={declined || isLoading}
                  onClick={() => setDeclined(true)}
                  size="sm"
                  variant="destructive"
                >
                  Decline
                </Button>
                {declined && (
                  <Button
                    onClick={() => setDeclined(false)}
                    size="sm"
                    variant="outline"
                  >
                    Cancel
                  </Button>
                )}
              </div>

              {declined && (
                <div className="space-y-2">
                  <Textarea
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    placeholder="Reason for decline"
                  />
                  <Button
                    disabled={declined && isLoading}
                    onClick={() => handleUpdateOrder('DECLINED')}
                    size="sm"
                  >
                    Submit
                  </Button>
                </div>
              )}
            </>
          )}
          {selectedOrder.status === 'APPROVED' && canManage && (
            <div className="flex gap-2">
              <Button
                disabled={isLoading}
                onClick={() => handleUpdateOrder('DELIVERED')}
                size="sm"
                variant="outline"
                className="border-green-500 cursor-pointer"
              >
                Mark Delivered
              </Button>
            </div>
          )}
          {selectedOrder.status === 'DELIVERED' &&
            Number(selectedOrder.staffId) === Number(profileId) && (
              <div className="flex gap-2">
                {!editingQuantities ? (
                  <Button
                    disabled={isLoading}
                    onClick={initializeReceivedItems}
                    size="sm"
                    variant="outline"
                    className="border-green-500 cursor-pointer"
                  >
                    Mark Received
                  </Button>
                ) : (
                  <>
                    <Button
                      disabled={isLoading}
                      onClick={handleMarkReceived}
                      size="sm"
                      className="bg-green-600 hover:bg-green-700"
                    >
                      Confirm Received
                    </Button>
                    <Button
                      onClick={() => {
                        setEditingQuantities(false);
                        setReceivedItems([]);
                      }}
                      size="sm"
                      variant="outline"
                    >
                      Cancel
                    </Button>
                  </>
                )}
              </div>
            )}
          {selectedOrder.status === 'RECEIVED' &&
            Number(selectedOrder.staffId) === Number(profileId) &&
            isReturnWindowOpen() &&
            !hasReturnedItems() && (
              <div className="space-y-2">
                <div className="flex gap-2">
                  {!editingReturns ? (
                    <Button
                      disabled={isLoading}
                      onClick={initializeReturnItems}
                      size="sm"
                      variant="outline"
                      className="border-red-500 cursor-pointer"
                    >
                      Return Items
                    </Button>
                  ) : (
                    <>
                      <Button
                        disabled={isLoading}
                        onClick={handleMarkReturned}
                        size="sm"
                        className="bg-red-600 hover:bg-red-700"
                      >
                        Confirm Return
                      </Button>
                      <Button
                        onClick={() => {
                          setEditingReturns(false);
                          setReturnItems([]);
                          setReturnReason('');
                        }}
                        size="sm"
                        variant="outline"
                      >
                        Cancel
                      </Button>
                    </>
                  )}
                </div>
                {editingReturns && (
                  <Textarea
                    value={returnReason}
                    onChange={(e) => setReturnReason(e.target.value)}
                    placeholder="Reason for return"
                  />
                )}
                <p className="text-xs text-gray-500">
                  Return window closes in:{' '}
                  <span className="font-mono text-red-500">{timeLeft}</span>
                </p>
              </div>
            )}
          <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            {selectedOrder?.staff && (
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Requested By:
                </p>
                <p className="text-sm">{selectedOrder.staff.fullName}</p>
              </div>
            )}
            <div className="font-semibold">
              <p className="text-sm font-medium text-gray-500">
                {selectedOrder.neededBy ? 'Needed By' : 'Created At'}
              </p>
              <p className="text-sm">
                {dayjs(
                  selectedOrder.neededBy || selectedOrder.createdAt
                ).format('DD MMM, YYYY HH:mm A')}
              </p>
            </div>
            {selectedOrder.approvedBy && (
              <div>
                <p className="text-sm font-medium text-gray-500">Approved By</p>
                <p className="text-sm">{selectedOrder.approvedBy}</p>
              </div>
            )}
            {selectedOrder.declinedBy && (
              <div>
                <p className="text-sm font-medium text-gray-500">Declined By</p>
                <p className="text-sm">{selectedOrder.declinedBy}</p>
              </div>
            )}
            {selectedOrder.deliveredBy && (
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Delivered By
                </p>
                <p className="text-sm">{selectedOrder.deliveredBy}</p>
              </div>
            )}
            {selectedOrder.orderType && (
              <div>
                <p className="text-sm font-medium text-gray-500">Order Type</p>
                <p className="text-sm">{selectedOrder.orderType}</p>
              </div>
            )}
            {selectedOrder.saleType && (
              <div>
                <p className="text-sm font-medium text-gray-500">Sale Type</p>
                <p className="text-sm">
                  {selectedOrder.saleType}
                </p>
              </div>
            )}
            {selectedOrder.paymentType && (
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Payment Method
                </p>
                <p className="text-sm">{selectedOrder.paymentType}</p>
              </div>
            )}
            {selectedOrder.servedBy && (
              <div>
                <p className="text-sm font-medium text-gray-500">Served By</p>
                <p className="text-sm">{selectedOrder.servedBy}</p>
              </div>
            )}

            <div>
              <p className="text-sm font-medium text-gray-500">Total Amount</p>
              <p className="text-sm font-semibold text-green-600">
                {currencyFormat(selectedOrder.totalAmount)}
              </p>
            </div>
            {selectedOrder.receivedTotalAmount && (
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Received Total
                </p>
                <p className="text-sm font-semibold text-green-600">
                  {currencyFormat(selectedOrder.receivedTotalAmount)}
                </p>
              </div>
            )}
            {selectedOrder.returnedTotalAmount && (
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Returned Total
                </p>
                <p className="text-sm font-semibold text-red-600">
                  {currencyFormat(selectedOrder.returnedTotalAmount)}
                </p>
              </div>
            )}
            {selectedOrder.returnedBy && (
              <div>
                <p className="text-sm font-medium text-gray-500">Returned By</p>
                <p className="text-sm">{selectedOrder.returnedBy}</p>
              </div>
            )}
            {selectedOrder.returnedAt && (
              <div>
                <p className="text-sm font-medium text-gray-500">Returned At</p>
                <p className="text-sm">
                  {dayjs(selectedOrder.returnedAt).format('DD MMM, YYYY HH:mm A')}
                </p>
              </div>
            )}
            {selectedOrder.returnReason && (
              <div>
                <p className="text-sm font-medium text-gray-500">Return Reason</p>
                <p className="text-sm">{selectedOrder.returnReason}</p>
              </div>
            )}
          </div>

          {selectedOrder.purpose && (
            <div>
              <p className="text-sm font-medium text-gray-500">Purpose</p>
              <p className="text-sm">{selectedOrder.purpose}</p>
            </div>
          )}

          {/* Order Items */}
          <div>
            <h3 className="text-lg font-semibold mb-3">
              Order Items{' '}
              {editingQuantities && (
                <span className="text-sm text-blue-600">
                  (Edit quantities to mark as received)
                </span>
              )}
              {editingReturns && (
                <span className="text-sm text-red-600">
                  (Edit quantities to return)
                </span>
              )}
            </h3>
            {selectedOrder.orderItems && selectedOrder.orderItems.length > 0 ? (
              <div className="space-y-2">
                {selectedOrder.orderItems.map((item: any, index: number) => {
                  const receivedItem = receivedItems.find(
                    (ri) => ri.id === item.id
                  );
                  const returnItem = returnItems.find(
                    (ri) => ri.id === item.id
                  );
                  const currentQuantity = editingQuantities
                    ? receivedItem?.quantity || item.quantity
                    : editingReturns
                      ? returnItem?.quantity || 0
                      : item.quantity;

                  return (
                    <div
                      key={index}
                      className="flex justify-between items-center p-3 border rounded-lg"   
                    >
                      <div className="flex-1">
                        <p className="font-medium">
                          {item.menuItem?.name || item.name || 'Unknown Item'}
                        </p>
                        <div className="flex items-center gap-4 mt-1">
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-500">
                              Quantity:
                            </span>
                            {editingQuantities ? (
                              <Input
                                type="number"
                                min="0"
                                max={item.quantity}
                                value={currentQuantity}
                                onChange={(e) =>
                                  updateReceivedQuantity(
                                    item.id,
                                    parseInt(e.target.value) || 0
                                  )
                                }
                                className="w-20 h-8"
                              />
                            ) : editingReturns ? (
                              <Input
                                type="number"
                                min="0"
                                max={item.receivedQuantity || 0}
                                value={currentQuantity}
                                onChange={(e) =>
                                  updateReturnQuantity(
                                    item.id,
                                    parseInt(e.target.value) || 0
                                  )
                                }
                                className="w-20 h-8"
                              />
                            ) : (
                              <span className="text-sm">{currentQuantity}</span>
                            )}
                          </div>
                          {item.receivedQuantity && (
                            <p className="text-sm text-green-600">
                              Received: {item.receivedQuantity}
                            </p>
                          )}
                          {item.returnedQuantity && (
                            <p className="text-sm text-red-600">
                              Returned: {item.returnedQuantity}
                            </p>
                          )}
                          <p className="text-sm text-gray-500">
                            Unit Price: {currencyFormat(item.unitPrice || 0)}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">
                          {currencyFormat(
                            currentQuantity * (item.unitPrice || 0)
                          )}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">
                No items found for this order
              </p>
            )}
          </div>

          {/* Returned Items Section */}
          {returnedItems && returnedItems.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-3 text-red-600">
                Returned Items
              </h3>
              <div className="space-y-2">
                {returnedItems.map((item: any, index: number) => (
                  <div
                    key={index}
                    className="flex justify-between items-center p-3 border border-red-200 rounded-lg"
                  >
                    <div className="flex-1">
                      <p className="font-medium">
                        {item.menuItem?.name || item.name || 'Unknown Item'}
                      </p>
                      <div className="">
                        <p className="text-sm text-red-600">
                          Returned Quantity: {item.returnedQuantity}
                        </p>
                        <p className="text-sm text-gray-500">
                          Reason: {item.reason}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-red-600">
                        {currencyFormat(item.returnedQuantity * (item.menuItem.unitPrice || 0))}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </Modal>
  );
}
