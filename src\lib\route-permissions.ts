'use client';

import { PERMISSIONS } from './types/permissions';
import { Paths } from '@/components/navigations/data';

// Ordered route-permission pairs to ensure proper priority
const orderedRoutePermissions = [
  [Paths.Dashboard, PERMISSIONS.DASHBOARD_VIEW],
  [Paths.Packages, PERMISSIONS.BOOKING_VIEW],
  [Paths.Feedbacks, PERMISSIONS.FEEDBACK_VIEW],
  [Paths.Referral, PERMISSIONS.REFERRAL_VIEW],
  [Paths.Cafeteria, PERMISSIONS.CAFETERIA_VIEW],
  [Paths.Reporting, PERMISSIONS.INCIDENT_VIEW],
  [Paths.Discounts, PERMISSIONS.REWARD_VIEW],
  [Paths.Transactions, PERMISSIONS.TRANSACTION_VIEW],
  [Paths.Staffs, PERMISSIONS.STAFF_VIEW],
  [Paths.Rewards, PERMISSIONS.REWARD_VIEW],
  [Paths.Location, PERMISSIONS.LOCATION_EDIT],
  [Paths.Forum, PERMISSIONS.FORUM_VIEW],
  [Paths.InnovationHub, PERMISSIONS.HUB_VIEW],
  [Paths.ProcessDictionary, PERMISSIONS.PROCESS_VIEW],
  [Paths.CHIS, PERMISSIONS.CHIS_VIEW],
  [Paths.AIAssistant, PERMISSIONS.AI_ASSISTANT_VIEW],
] as const;

// Map paths to required permissions
export const pathPermissionMap: Record<string, string> = Object.fromEntries(
  orderedRoutePermissions
);

export const findFirstAccessibleRoute = (
  permissions: string[]
): string | null => {
  for (const [route, perm] of orderedRoutePermissions) {
    if (permissions.includes(perm)) {
      return route;
    }
  }
  return null;
};
