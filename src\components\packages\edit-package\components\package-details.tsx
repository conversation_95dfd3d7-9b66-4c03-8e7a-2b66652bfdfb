'use client';

import { useEffect, useState } from 'react';
import { Form } from '@/components/ui/form';
import {
  InputField,
  FormRow,
  SwitchField,
  InputTextArea,
  CurrencyInputField,
} from '@/components/common/form';
import { Check, PencilIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'sonner';
import {
  formSchema,
  type ProductFormValues,
} from '@/components/validations/package';
import { Label } from '@/components/ui/label';
import { MultiSelect } from '@/components/common/multi-select';
import { GetPackageTest } from '@/api/data';
import { GetPublicCategories } from '@/api/data';
import { GetProfile } from '@/api/staff';
import { ComboBox } from '@/components/common/single-select';
import { myApi } from '@/api/fetcher';

interface PackageDetailsProps {
  packageData: any;
  onUpdateSuccess?: () => void;
}

const PackageDetails = ({
  packageData,
  onUpdateSuccess,
}: PackageDetailsProps) => {
  const { profile } = GetProfile();
  const [formState, setFormState] = useState({
    isSubmitting: false,
    isFieldsEditable: false,
    selectedTests: [] as string[],
  });

  // Destructure state for easier access
  const { isSubmitting, isFieldsEditable, selectedTests } = formState;

  // Update specific state properties
  const updateFormState = (updates: Partial<typeof formState>) => {
    setFormState((prev) => ({ ...prev, ...updates }));
  };

  // API data hooks
  const { test } = GetPackageTest();
  const { category } = GetPublicCategories();

  // Data from API responses
  const testData = test?.data || [];
  const categoryData = category?.data || [];

  interface Test {
    id: string;
    name: string;
  }

  const form = useForm<ProductFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      description: '',
      test: [],
      slot: 0,
      basePrice: undefined,
      categoryId: 0,
    },
    mode: 'onChange',
  });

  // Load existing data
  useEffect(() => {
    if (packageData) {
      // Populate form with existing data
      form.reset({
        name: packageData.name || '',
        description: packageData.description || '',
        test: packageData.tests?.map((test: { name: any }) => test.name) || [],
        slot: packageData.totalSlot || 0,
        basePrice: packageData.basePrice,
        categoryId: packageData.categoryId || 0,
        bonus: packageData.bonusApplicable,
        registration: packageData.includeHospitalReg,
      });

      // Set selected tests
      if (packageData.tests && Array.isArray(packageData.tests)) {
        const testIds = packageData.tests.map((test: any) => test.id);
        updateFormState({
          selectedTests: testIds,
        });
      }
    }
  }, [packageData, form]);

  const onSubmit = async (data: ProductFormValues) => {
    if (selectedTests.length === 0) {
      form.setError('test', {
        type: 'manual',
        message: 'At least one test must be selected',
      });
      return;
    }
    const selectedTestIds = selectedTests;

    try {
      updateFormState({ isSubmitting: true });
      const res = await myApi.patch('/package/update-package', {
        id: packageData.id,
        name: data.name,
        updatedBy: profile.data.fullName,
        testIds: selectedTestIds,
        basePrice: data.basePrice,
        description: data.description,
        totalSlot: data.slot,
        categoryId: data.categoryId,
        bonusApplicable: data.bonus,
        includeHospitalReg: data.registration,
      });
      updateFormState({ isSubmitting: false });
      if (res.status === 200) {
        toast.success(
          res.data.message || 'Package details updated successfully'
        );
        updateFormState({ isFieldsEditable: false });
        if (onUpdateSuccess) {
          onUpdateSuccess();
        }
      }
    } catch (error) {
      updateFormState({ isSubmitting: false });
      toast.error('An error occurred. Please try again.');
    }
  };

  return (
    <div className="bg-white dark:bg-zinc-900/70 rounded-md border p-6">
      {/* Edit button */}
      <div className="flex justify-end mb-4">
        <Button
          variant="outline"
          type="button"
          onClick={() =>
            updateFormState({ isFieldsEditable: !isFieldsEditable })
          }
          className="flex items-center gap-2"
        >
          <PencilIcon className="w-4 h-4" />
          {isFieldsEditable ? 'Cancel Edit' : 'Edit Details'}
        </Button>
      </div>

      <Form {...form}>
        <form className="space-y-4">
          {/* Basic information section */}
          <FormRow>
            <SwitchField
              control={form.control}
              name="bonus"
              label="Bonus Applicable"
              disabled={!isFieldsEditable}
            />
            <SwitchField
              control={form.control}
              name="registration"
              label="Registration included"
              disabled={!isFieldsEditable}
            />
          </FormRow>
          <FormRow>
            <ComboBox
              control={form.control}
              name="categoryId"
              label="Category"
              placeholder="Select a category"
              options={categoryData || []}
              searchPlaceholder="Search categories..."
              emptyMessage="No categories found."
              disabled={!isFieldsEditable}
            />
            <InputField
              control={form.control}
              name="name"
              label="Package Name"
              placeholder="Enter the package name"
              type="text"
              disabled={!isFieldsEditable}
            />
            <InputField
              control={form.control}
              name="slot"
              label="Total Slot"
              placeholder="0"
              type="number"
              min={0}
              disabled={!isFieldsEditable}
            />
            <CurrencyInputField
              control={form.control}
              name="basePrice"
              label="Base Price"
              placeholder="The normal price"
              disabled={!isFieldsEditable}
            />
          </FormRow>

          {/* Tests selection section */}
          <div>
            <Label htmlFor="tests">Select Tests</Label>
            <MultiSelect
              options={testData || []}
              selected={selectedTests}
              onChange={(tests) => {
                // When a test is selected or deselected, we need to update the selectedTests state
                // with the new array of test IDs
                updateFormState({ selectedTests: tests });
              }}
              placeholder="Select tests"
              valueField="id"
              labelField="name"
              badgeClassName="bg-primary text-primary-foreground hover:bg-primary/90"
              disabled={!isFieldsEditable}
              renderOption={(test, isSelected) => (
                <div className="flex flex-col">
                  <div className="flex items-center">
                    <div className="mr-2 flex h-4 w-4 items-center justify-center">
                      {isSelected ? <Check className="h-4 w-4" /> : null}
                    </div>
                    <span>{(test as Test).name}</span>
                  </div>
                </div>
              )}
            />
            {selectedTests.length === 0 && form.formState.isSubmitted && (
              <p className="text-sm text-red-500 mt-1">
                At least one test must be selected
              </p>
            )}
          </div>

          {/* Description section */}
          <InputTextArea
            control={form.control}
            name="description"
            label="Package Description"
            placeholder="Enter package description"
            disabled={!isFieldsEditable}
          />
        </form>

        {/* Submit button - only show when editing is enabled */}
        {isFieldsEditable && (
          <div className="text-center py-4">
            <Button
              className="cursor-pointer"
              type="button"
              onClick={form.handleSubmit(onSubmit)}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Updating...' : 'Update Package'}
            </Button>
          </div>
        )}
      </Form>
    </div>
  );
};

export default PackageDetails;
