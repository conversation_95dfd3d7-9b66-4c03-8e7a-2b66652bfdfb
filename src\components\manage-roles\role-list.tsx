import React from 'react';
import { Ellipsis } from 'lucide-react';
import { GetRoleList } from '@/api/staff';
import { EmptyState, LoadingState } from '@/components/common/dataState';

interface RoleListProps {
  onRoleSelect: (role: any) => void;
  onMutateReady?: (mutate: () => void) => void;
}

const RoleList: React.FC<RoleListProps> = ({ onRoleSelect, onMutateReady }) => {
  const { roleList, mutate, isLoading } = GetRoleList();

  React.useEffect(() => {
    if (onMutateReady && mutate) {
      onMutateReady(mutate);
    }
  }, [mutate, onMutateReady]);

  return (
    <div className="w-full overflow-x-auto rounded-t-lg shadow-md">
      <table className="w-full table-auto text-left text-xs">
        <thead className="bg-primary text-gray-100 dark:text-black">
          <tr>
            <th className="table-style">S/N</th>
            <th className="table-style">Role Name</th>
            <th className="table-style">Description</th>
            <th className="table-style">Action</th>
          </tr>
        </thead>
        <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
          {roleList?.map((role: any, index: number) => (
            <tr
              className="text-xs text-[#062A55] dark:text-white"
              key={role.id}
            >
              <td className="table-style">{index + 1}</td>
              <td className="table-style">{role.name}</td>
              <td className="table-style">
                {role.description || 'No description'}
              </td>
              <td className="table-style">
                <Ellipsis
                  onClick={() => onRoleSelect(role)}
                  className="w-4 h-4 cursor-pointer"
                />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      {isLoading ? <LoadingState /> : !roleList?.length ? <EmptyState /> : null}
    </div>
  );
};

export default RoleList;
