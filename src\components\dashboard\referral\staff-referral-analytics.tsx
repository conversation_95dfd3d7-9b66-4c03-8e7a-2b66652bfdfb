'use client';

import { useState } from 'react';
import {
  TrendingUp,
  TrendingDown,
  Users,
  MapPin,
  Award,
  DollarSign,
} from 'lucide-react';
import {
  Bar,
  <PERSON><PERSON>hart,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Pie,
  <PERSON>Chart,
  Cell,
} from 'recharts';
import YearSelect from '../../common/year-select';
import { GetStaffReferralAnalytics } from '@/api/analytics';
import { formatValue, numberFormat } from '@/lib/utils';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, XCircle } from 'lucide-react';

// Define chart configuration
const chartConfig = {
  referrals: {
    label: 'Referrals',
    theme: {
      light: '#BD9A3D',
      dark: '#BD9A3D',
    },
  },
  commission: {
    label: 'Commission',
    theme: {
      light: '#2196F3',
      dark: '#2196F3',
    },
  },
  gudu: {
    label: 'Gudu',
    theme: {
      light: '#BD9A3D',
      dark: '#BD9A3D',
    },
  },
  vi: {
    label: 'VI',
    theme: {
      light: '#2196F3',
      dark: '#2196F3',
    },
  },
  gwarimpa: {
    label: 'Gwarimpa',
    theme: {
      light: '#1A1A1A',
      dark: '#1A1A1A',
    },
  },
} satisfies ChartConfig;

const COLORS = ['#BD9A3D', '#2196F3', '#1A1A1A'];

export function StaffReferralAnalytics() {
  const [year, setYear] = useState<string>(new Date().getFullYear().toString());
  const [activeTab, setActiveTab] = useState('overview');

  const handleYearChange = (selectedYear: string) => {
    setYear(selectedYear);
  };

  // Use the real API function with the year parameter
  const { referralStats, referralStatsLoading, referralStatsError } =
    GetStaffReferralAnalytics(`year=${year}`);

  // Handle loading state
  if (referralStatsLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px]">
        <Loader2
          className="w-8 h-8 animate-spin"
          style={{ color: '#BD9A3D' }}
        />
        <p className="text-muted-foreground mt-4">
          Loading staff referral analytics...
        </p>
      </div>
    );
  }

  // Handle error state
  if (referralStatsError) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px]">
        <XCircle className="w-8 h-8 mb-4" style={{ color: '#2196F3' }} />
        <p>Error loading staff referral analytics. Please try again later.</p>
      </div>
    );
  }

  // If no data is available, use mock data for development
  const data = referralStats?.data.data;

  console.log(data, "referral data")

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-4">
        {/* Summary Cards */}
        <Card className="w-full md:w-[calc(33.33%-1rem)]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Total Referrals
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalReferrals}</div>
            <div className="flex items-center mt-1 text-xs">
              {data.recentTrend.direction === 'up' ? (
                <TrendingUp
                  className="w-3 h-3 mr-1"
                  style={{ color: '#BD9A3D' }}
                />
              ) : (
                <TrendingDown
                  className="w-3 h-3 mr-1"
                  style={{ color: '#2196F3' }}
                />
              )}
              <span
                style={{
                  color:
                    data.recentTrend.direction === 'up' ? '#BD9A3D' : '#2196F3',
                }}
              >
                {data.recentTrend.percent}% from last month
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="w-full md:w-[calc(33.33%-1rem)]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Staff</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.activeStaff}</div>
            <div className="flex items-center mt-1 text-xs">
              <Users className="w-3 h-3 mr-1" style={{ color: '#2196F3' }} />
              <span className="text-muted-foreground">
                Staff with active referral codes
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="w-full md:w-[calc(33.33%-1rem)]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Total Commission
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {numberFormat(data.totalCommission)}
            </div>
            <div className="flex items-center mt-1 text-xs">
              <span className="text-[#BD9A3D] mr-1">₦</span>
              <span className="text-muted-foreground">
                Earned from referrals
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for different analytics views */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-3 mb-4">
          <TabsTrigger value="overview" className="flex items-center gap-1">
            <Award
              className="w-4 h-4"
              style={{
                color: activeTab === 'overview' ? '#BD9A3D' : 'inherit',
              }}
            />{' '}
            Overview
          </TabsTrigger>
          <TabsTrigger value="staff" className="flex items-center gap-1">
            <Users
              className="w-4 h-4"
              style={{ color: activeTab === 'staff' ? '#BD9A3D' : 'inherit' }}
            />{' '}
            Top Staff
          </TabsTrigger>
          <TabsTrigger value="location" className="flex items-center gap-1">
            <MapPin
              className="w-4 h-4"
              style={{
                color: activeTab === 'location' ? '#BD9A3D' : 'inherit',
              }}
            />{' '}
            By Location
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent
          value="overview"
          className="mt-0 animate-in fade-in-50 duration-300"
        >
          <Card>
            <CardHeader>
              <div className="flex flex-wrap gap-4 justify-between">
                <div>
                  <CardTitle>Monthly Referrals</CardTitle>
                  <CardDescription>
                    Referrals and commission by month
                  </CardDescription>
                </div>
                <div>
                  <YearSelect onChange={handleYearChange} />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <ChartContainer
                className="aspect-auto h-[300px]"
                config={chartConfig}
              >
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={data.monthlyData}>
                    <CartesianGrid vertical={false} strokeDasharray="3 3" />
                    <XAxis
                      dataKey="month"
                      tickLine={false}
                      tickMargin={10}
                      axisLine={false}
                      tickFormatter={(value) => value.slice(0, 3)}
                    />
                    <YAxis
                      yAxisId="left"
                      orientation="left"
                      stroke="#2196F3"
                      tickLine={false}
                      tickMargin={10}
                    />
                    <YAxis
                      yAxisId="right"
                      orientation="right"
                      stroke="#BD9A3D"
                      tickLine={false}
                      tickMargin={10}
                      tickFormatter={(value) => `₦${value / 1000}K`}
                    />
                    <Tooltip
                      formatter={(value, name) => {
                        if (name === 'referrals')
                          return [`${value} referrals`, 'Referrals'];
                        if (name === 'commission')
                          return [
                            `${numberFormat(value as number)}`,
                            'Commission',
                          ];
                        return [value, name];
                      }}
                    />
                    <Legend />
                    <Bar
                      yAxisId="left"
                      dataKey="referrals"
                      fill="#2196F3"
                      name="Referrals"
                    />
                    <Bar
                      yAxisId="right"
                      dataKey="commission"
                      fill="#BD9A3D"
                      name="Commission"
                    />
                  </BarChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Top Staff Tab */}
        <TabsContent
          value="staff"
          className="mt-0 animate-in fade-in-50 duration-300"
        >
          <Card>
            <CardHeader>
              <CardTitle>Top Performing Staff</CardTitle>
              <CardDescription>Staff with the most referrals</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data?.topStaff.map((staff: any, index: number) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10">
                        <span style={{ color: '#BD9A3D' }}>{index + 1}</span>
                      </div>
                      <div>
                        <p className="font-medium">{staff.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {staff.location.name}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{staff.referrals} referrals</p>
                      <p className="text-xs text-muted-foreground">
                        {numberFormat(staff.commission)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Location Tab */}
        <TabsContent
          value="location"
          className="mt-0 animate-in fade-in-50 duration-300"
        >
          <Card>
            <CardHeader>
              <CardTitle>Referrals by Location</CardTitle>
              <CardDescription>
                Distribution of referrals across locations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer
                className="aspect-auto h-[300px]"
                config={chartConfig}
              >
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={data.locationData}>
                    <CartesianGrid vertical={false} strokeDasharray="3 3" />
                    <XAxis
                      dataKey="location"
                      tickLine={false}
                      tickMargin={10}
                      axisLine={false}
                    />
                    <YAxis
                      yAxisId="left"
                      orientation="left"
                      stroke="#2196F3"
                      tickLine={false}
                      tickMargin={10}
                    />
                    <YAxis
                      yAxisId="right"
                      orientation="right"
                      stroke="#BD9A3D"
                      tickLine={false}
                      tickMargin={10}
                      tickFormatter={(value) => `₦${value / 1000}K`}
                    />
                    <Tooltip
                      formatter={(value, name) => {
                        if (name === 'referrals')
                          return [`${value} referrals`, 'Referrals'];
                        if (name === 'commission')
                          return [
                            `${numberFormat(value as number)}`,
                            'Commission',
                          ];
                        return [value, name];
                      }}
                    />
                    <Legend />
                    <Bar
                      yAxisId="left"
                      dataKey="referrals"
                      fill="#2196F3"
                      name="Referrals"
                    />
                    <Bar
                      yAxisId="right"
                      dataKey="commission"
                      fill="#BD9A3D"
                      name="Commission"
                    />
                  </BarChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
