'use client';

import { useState } from 'react';
import { BookUser, Shield, Users } from 'lucide-react';
import Staff from './data-table';
import Department from './department/data-table';
import RoleManagement from '../manage-roles';
import { Badge } from '@/components/ui/badge';

export default function StaffPage() {
  const [switchView, setSwitchView] = useState<
    'staff' | 'department' | 'roles'
  >('staff');

  return (
    <>
      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
        {switchView === 'roles' ? (
          <Shield className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
        ) : switchView === 'department' ? (
          <BookUser className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
        ) : (
          <Users className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
        )}
        {switchView === 'department'
          ? 'Department Management'
          : switchView === 'roles'
            ? 'Role Management'
            : 'Staff Management'}
      </h2>
      <div className="space-x-2 space-y-2">
        <Badge
          variant={switchView === 'staff' ? 'default' : 'outline'}
          className="h-7 cursor-pointer"
          onClick={() => setSwitchView('staff')}
        >
          Staff Management
        </Badge>
        <Badge
          variant={switchView === 'department' ? 'default' : 'outline'}
          className="h-7 cursor-pointer"
          onClick={() => setSwitchView('department')}
        >
          Manage Department
        </Badge>
        <Badge
          variant={switchView === 'roles' ? 'default' : 'outline'}
          className="h-7 cursor-pointer"
          onClick={() => setSwitchView('roles')}
        >
          Manage Roles
        </Badge>
      </div>
      <div className="mt-4 bg-white dark:bg-[#0F0F12] rounded-xl sm:p-4 flex flex-col items-start justify-start sm:border border-gray-200 dark:border-[#1F1F23]">
        {switchView === 'staff' && <Staff />}
        {switchView === 'department' && <Department />}
        {switchView === 'roles' && <RoleManagement />}
      </div>
    </>
  );
}
