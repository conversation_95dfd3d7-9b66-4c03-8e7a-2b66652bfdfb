'use client';

import { useFormContext } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle } from 'lucide-react';

export default function StepReview() {
  const { getValues } = useFormContext();
  const data = getValues();

  const sections = [
    {
      title: 'Personal Information',
      key: 'personal',
      data: {
        dob: data.dob,
        nationality: data.nationality,
        ethnicity: data.ethnicity,
      },
    },
    { title: 'Home Details', key: 'home', data: data.home },
    {
      title: 'Consultancy Details',
      key: 'consultancy',
      data: data.consultancy,
    },
    { title: 'Privileges', key: 'privileges', data: data.privileges },
    { title: 'Referees', key: 'referees', data: data.referees },
    {
      title: 'Additional Information',
      key: 'additional',
      data: data.additional,
    },
    { title: 'Cover & Subcontracting', key: 'cover', data: data.cover },
    {
      title: 'Indemnity & Declarations',
      key: 'indemnity',
      data: data.indemnity,
    },
  ];

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <CheckCircle className="w-12 h-12 text-green-500 mx-auto" />
        <h3 className="text-lg font-semibold text-foreground">
          Application Review
        </h3>
        <p className="text-sm text-muted-foreground">
          Please review all sections below before submitting your privileging
          application.
        </p>
      </div>

      <div className="grid gap-4">
        {sections.map((section) => {
          const hasData = section.data && Object.keys(section.data).length > 0;
          return (
            <Card
              key={section.key}
              className={hasData ? 'border-green-200' : 'border-orange-200'}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base">{section.title}</CardTitle>
                  <Badge variant={hasData ? 'success' : 'secondary'}>
                    {hasData ? 'Complete' : 'Incomplete'}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                {hasData ? (
                  <div className="text-sm text-muted-foreground">
                    {Object.keys(section.data).length} field(s) completed
                  </div>
                ) : (
                  <div className="text-sm text-orange-600">
                    No information provided
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="bg-muted/50 p-4 rounded-lg">
        <h4 className="font-medium text-foreground mb-2">Important Notes:</h4>
        <ul className="text-sm text-muted-foreground space-y-1">
          <li>• Ensure all required fields are completed accurately</li>
          <li>
            • Your application will be reviewed by the medical staff office
          </li>
          <li>• You will be notified of the outcome via email</li>
          <li>• Additional documentation may be requested</li>
        </ul>
      </div>
    </div>
  );
}
