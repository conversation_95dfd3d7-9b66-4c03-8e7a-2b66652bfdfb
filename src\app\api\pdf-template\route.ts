import { NextRequest, NextResponse } from 'next/server';
import { readFileSync } from 'fs';
import { join } from 'path';

export async function GET(request: NextRequest) {
  try {
    // Path to the privileges.pdf file in the lib folder
    const pdfPath = join(process.cwd(), 'src', 'lib', 'privileges.pdf');

    // Read the PDF file
    const pdfBuffer = readFileSync(pdfPath);

    // Return the PDF with appropriate headers
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'inline; filename="privileges.pdf"',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      },
    });
  } catch (error: unknown) {
    console.error('Error serving privileges.pdf from lib folder:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: 'privileges.pdf not found in lib folder', details: errorMessage },
      { status: 404 }
    );
  }
}
